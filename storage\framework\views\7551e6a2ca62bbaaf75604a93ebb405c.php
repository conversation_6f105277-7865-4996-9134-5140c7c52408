<?php $__env->startSection('title', __('Customs Clearance Details')); ?>

<!-- Vendor Styles -->
<?php $__env->startSection('vendor-style'); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss', 'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss', 'resources/assets/vendor/libs/select2/select2.scss', 'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss']); ?>
<?php $__env->stopSection(); ?>

<!-- Page Styles -->
<?php $__env->startSection('page-style'); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css']); ?>
    <style>
        .timeline {
            list-style: none;
            padding: 0;
            position: relative;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #ccc;
        }

        .timeline-item {
            position: relative;
            padding-left: 40px;
            margin-bottom: 20px;
        }

        .timeline-point {
            position: absolute;
            left: 7px;
            top: 5px;
            width: 16px;
            height: 16px;
            background: #0d6efd;
            border-radius: 50%;
        }

        .info-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .info-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .status-badge {
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
            border-radius: 50px;
        }

        .detail-row {
            border-bottom: 1px solid #f0f0f0;
            padding: 0.75rem 0;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 600;
            color: #495057;
            min-width: 150px;
        }

        .detail-value {
            color: #6c757d;
        }

        .file-icon {
            font-size: 3rem;
            color: #6c757d;
        }

        .gradient-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .card-header-custom {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .stats-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            color: white;
        }

        .delivery-section {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            border: none;
            color: white;
        }
    </style>
<?php $__env->stopSection(); ?>

<!-- Vendor Scripts -->
<?php $__env->startSection('vendor-script'); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js', 'resources/assets/vendor/libs/select2/select2.js', 'resources/assets/vendor/libs/sweetalert2/sweetalert2.js']); ?>
<?php $__env->stopSection(); ?>

<!-- Page Scripts -->
<?php $__env->startSection('page-script'); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/ajax.js']); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/admin/customs-clearances/show.js']); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="container my-4">
        <div class="row">
            <!-- تفاصيل التخليص الجمركي -->
            <div class="col-lg-8 col-md-12">
                <div class="card mb-4 info-card">
                    <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-dark">
                            <i class="ti ti-file-text me-2 text-primary"></i>
                            <?php echo e(__('Customs Clearance Details')); ?> #<?php echo e($data->id); ?>

                        </h5>

                        <div class="d-flex gap-2">


                            <a href="<?php echo e(route('admin.customs-clearances.index')); ?>" class="btn btn-outline-secondary">
                                <i class="ti ti-arrow-left me-1"></i>
                                Back to List
                            </a>

                        </div>
                    </div>

                    <div class="card-body">
                        <!-- معلومات أساسية -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="detail-row d-flex">
                                    <span class="detail-label"><?php echo e(__('Owner')); ?>:</span>
                                    <span class="detail-value"><?php echo e(__('Administrator')); ?></span>
                                </div>



                                <div class="detail-row d-flex">
                                    <span class="detail-label"><?php echo e(__('Name')); ?>:</span>
                                    <span class="detail-value"><?php echo e($data->owner->name); ?></span>
                                </div>

                            </div>
                            <div class="col-md-6">
                                <div class="detail-row d-flex">
                                    <span class="detail-label"><?php echo e(__('Created At')); ?>:</span>
                                    <span class="detail-value"><?php echo e($data->created_at->format('Y-m-d H:i')); ?></span>
                                </div>
                                <div class="detail-row d-flex">
                                    <span class="detail-label"><?php echo e(__('Updated At')); ?>:</span>
                                    <span class="detail-value"><?php echo e($data->updated_at->format('Y-m-d H:i')); ?></span>
                                </div>
                                <?php if($data->clearance_agent_id && $data->clearanceAgent): ?>
                                    <div class="detail-row d-flex">
                                        <span class="detail-label"><?php echo e(__('Assigned Agent')); ?>:</span>
                                        <span class="detail-value"><?php echo e($data->clearanceAgent->name); ?></span>
                                    </div>
                                <?php endif; ?>
                                <?php if($data->price): ?>
                                    <div class="detail-row d-flex">
                                        <span class="detail-label"><?php echo e(__('Price')); ?>:</span>
                                        <span class="detail-value"><?php echo e(number_format($data->price, 2)); ?>

                                            <?php echo e(__('SAR')); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php if($data->clearance_agent_id && $data->clearanceAgent): ?>
                            <div class="card mb-4 shadow-sm">
                                <div
                                    class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0 text-dark">
                                        <i class="fas fa-user me-2 text-primary"></i> <?php echo e(__('Assigned Driver')); ?>

                                    </h6>
                                    
                                </div>
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-2 text-center">
                                            <?php if($data->clearanceAgent->image): ?>
                                                <img src="<?php echo e(asset('storage/' . $data->clearanceAgent->image)); ?>"
                                                    alt="<?php echo e($data->clearanceAgent->name); ?>"
                                                    class="img-fluid rounded-circle shadow-sm mb-2"
                                                    style="width: 80px; height: 80px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center"
                                                    style="width: 80px; height: 80px;">
                                                    <i class="fas fa-user fa-2x"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="col-md-10">
                                            <h5 class="mb-1"><?php echo e($data->clearanceAgent->name); ?></h5>
                                            <p class="mb-1">
                                                <i class="fas fa-phone-alt me-1 text-success"></i>
                                                <a
                                                    href="tel:<?php echo e($data->clearanceAgent->phone); ?>"><?php echo e($data->clearanceAgent->phone); ?></a>
                                            </p>
                                            <?php if($data->clearanceAgent->email): ?>
                                                <p class="mb-1">
                                                    <i class="fas fa-envelope me-1 text-warning"></i>
                                                    <a
                                                        href="mailto:<?php echo e($data->clearanceAgent->email); ?>"><?php echo e($data->clearanceAgent->email); ?></a>
                                                </p>
                                            <?php endif; ?>
                                            <?php if($data->clearanceAgent->vehicle_size): ?>
                                                <p class="mb-1">
                                                    <i class="fas fa-truck me-1 text-primary"></i>
                                                    <?php echo e($data->clearanceAgent->vehicle_size->name); ?>

                                                </p>
                                            <?php endif; ?>
                                            <?php if($data->clearanceAgent->team): ?>
                                                <p class="mb-0">
                                                    <i class="fas fa-users me-1 text-info"></i>
                                                    <?php echo e(__('Team')); ?>: <?php echo e($data->clearanceAgent->team->name); ?>

                                                </p>
                                            <?php endif; ?>


                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-1"></i>
                                <?php echo e(__('Clearance agent not assigned yet')); ?>

                            </div>
                        <?php endif; ?>

                        <!-- إحصائيات المهمة -->
                        <div class="row g-3 mb-4">
                            <div class="col-md-3 col-sm-6">
                                <div class="card stats-card text-white h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-dollar-sign fs-2 mb-2"></i>
                                        <h6 class="card-title"><?php echo e(__('Total Price')); ?></h6>
                                        <h4 class="mb-0">
                                            <?php echo e($data->total_price ? number_format($data->total_price, 2) : '0.00'); ?> SAR
                                        </h4>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="card bg-warning text-white h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-percentage fs-2 mb-2"></i>
                                        <h6 class="card-title"><?php echo e(__('Commission')); ?></h6>
                                        <h4 class="mb-0">
                                            <?php echo e($data->commission ? number_format($data->commission, 2) : '0.00'); ?> SAR
                                        </h4>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="card bg-info text-white h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-info-circle fs-2 mb-2"></i>
                                        <h6 class="card-title"><?php echo e(__('Status')); ?></h6>
                                        <span class="status-badge bg-white text-info fw-bold">
                                            <?php echo e(ucfirst($data->status)); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="card bg-<?php echo e($data->closed ? 'danger' : 'success'); ?> text-white h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-lock<?php echo e($data->closed ? '' : '-open'); ?> fs-2 mb-2"></i>
                                        <h6 class="card-title"><?php echo e(__('Task Status')); ?></h6>
                                        <span
                                            class="status-badge bg-white text-<?php echo e($data->closed ? 'danger' : 'success'); ?> fw-bold">
                                            <?php echo e($data->closed ? __('Closed') : __('Open')); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- العروض -->
                        <?php if($data->offers && $data->offers->count() > 0): ?>
                            <div class="row mt-4">
                                <div class="col-12">
                                    <h6 class="mb-3">
                                        <i class="ti ti-currency-dollar me-2"></i><?php echo e(__('Offers')); ?>

                                    </h6>
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th><?php echo e(__('Agent')); ?></th>
                                                    <th><?php echo e(__('Price')); ?></th>
                                                    <th><?php echo e(__('Description')); ?></th>
                                                    <th><?php echo e(__('Date')); ?></th>
                                                    <th><?php echo e(__('Status')); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $data->offers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $offer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td><?php echo e($offer->clearanceAgent->name ?? __('N/A')); ?></td>
                                                        <td><?php echo e(number_format($offer->price, 2)); ?> <?php echo e(__('SAR')); ?></td>
                                                        <td><?php echo e($offer->description ?? __('N/A')); ?></td>
                                                        <td><?php echo e($offer->created_at->format('Y-m-d H:i')); ?></td>
                                                        <td>

                                                            <span
                                                                class="badge <?php echo e($offer->accepted ? 'bg-success' : ($offer->accepted === 'rejected' ? 'bg-danger' : 'bg-warning')); ?>">
                                                                <?php echo e($offer->accepted ? __('Accepted') : ($offer->accepted === 'rejected' ? __('Rejected') : __('Pending'))); ?>

                                                            </span>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>


                <!-- بيانات إضافية -->
                <?php if($data->additional_data): ?>
                    <div class="card mb-4 info-card">
                        <div class="card-header bg-white border-bottom">
                            <h5 class="mb-0 text-dark">
                                <i class="fas fa-layer-group me-2 text-primary"></i>
                                <?php echo e(__('Additional Data')); ?>

                            </h5>
                        </div>

                        <div class="card-body mt-3">
                            <?php if(is_array($data->additional_data) && count($data->additional_data) > 0): ?>
                                <div class="row">
                                    <?php $__currentLoopData = $data->additional_data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-md-6 mb-4">
                                            <div class="border rounded p-3 h-100 ">
                                                <h6 class="text-muted mb-2">

                                                    <?php echo e($field['label']); ?>

                                                </h6>

                                                <?php switch($field['type']):
                                                    case ('text'): ?>
                                                    <?php case ('string'): ?>

                                                    <?php case ('number'): ?>
                                                        <p class="mb-0"><?php echo e($field['value']); ?></p>
                                                    <?php break; ?>

                                                    <?php case ('image'): ?>
                                                        <img src="<?php echo e(asset('storage/' . $field['value'])); ?>"
                                                            alt="<?php echo e($field['label']); ?>" class="img-fluid rounded border"
                                                            style="max-height: 200px; object-fit: cover;">
                                                    <?php break; ?>

                                                    <?php case ('file'): ?>
                                                        <?php
                                                            $ext = strtolower(
                                                                pathinfo($field['value'], PATHINFO_EXTENSION),
                                                            );
                                                            $icons = [
                                                                'pdf' => 'ti ti-file-text',
                                                                'doc' => 'ti ti-file-description',
                                                                'docx' => 'ti ti-file-description',
                                                                'xls' => 'ti ti-file-spreadsheet',
                                                                'xlsx' => 'ti ti-file-spreadsheet',
                                                                'ppt' => 'ti ti-presentation',
                                                                'pptx' => 'ti ti-presentation',
                                                            ];
                                                            $iconClass = $icons[$ext] ?? 'ti ti-file';
                                                        ?>

                                                        <a href="<?php echo e(asset('storage/' . $field['value'])); ?>" target="_blank"
                                                            class="d-flex align-items-center text-decoration-none mt-1">
                                                            <i class="<?php echo e($iconClass); ?> me-2 fs-4 text-primary"></i>
                                                            <span class="text-truncate"><?php echo e(basename($field['value'])); ?></span>
                                                        </a>
                                                    <?php break; ?>

                                                    <?php case ('file_with_text'): ?>
                                                        <?php if($field['value']): ?>
                                                            <?php
                                                                $ext = strtolower(
                                                                    pathinfo($field['value'], PATHINFO_EXTENSION),
                                                                );
                                                                $icons = [
                                                                    'pdf' => 'ti ti-file-text',
                                                                    'doc' => 'ti ti-file-description',
                                                                    'docx' => 'ti ti-file-description',
                                                                    'xls' => 'ti ti-file-spreadsheet',
                                                                    'xlsx' => 'ti ti-file-spreadsheet',
                                                                    'ppt' => 'ti ti-presentation',
                                                                    'pptx' => 'ti ti-presentation',
                                                                ];
                                                                $iconClass = $icons[$ext] ?? 'ti ti-file';
                                                            ?>

                                                            <div class="d-flex align-items-center mb-2">
                                                                <a href="<?php echo e(asset('storage/' . $field['value'])); ?>"
                                                                    target="_blank"
                                                                    class="d-flex align-items-center text-decoration-none">
                                                                    <i class="<?php echo e($iconClass); ?> me-2 fs-4 text-primary"></i>
                                                                    <span
                                                                        class="text-truncate"><?php echo e(basename($field['value'])); ?></span>
                                                                </a>
                                                            </div>
                                                        <?php endif; ?>

                                                        <?php if(isset($field['text']) && $field['text']): ?>
                                                            <div class="mt-2">
                                                                <small class="text-muted">Additional Info:</small>
                                                                <p class="mb-0 fw-medium"><?php echo e($field['text']); ?></p>
                                                            </div>
                                                        <?php endif; ?>
                                                    <?php break; ?>

                                                    <?php case ('file_expiration_date'): ?>
                                                        <?php if($field['value']): ?>
                                                            <?php
                                                                $ext = strtolower(
                                                                    pathinfo($field['value'], PATHINFO_EXTENSION),
                                                                );
                                                                $icons = [
                                                                    'pdf' => 'ti ti-file-text',
                                                                    'doc' => 'ti ti-file-description',
                                                                    'docx' => 'ti ti-file-description',
                                                                    'xls' => 'ti ti-file-spreadsheet',
                                                                    'xlsx' => 'ti ti-file-spreadsheet',
                                                                    'ppt' => 'ti ti-presentation',
                                                                    'pptx' => 'ti ti-presentation',
                                                                ];
                                                                $iconClass = $icons[$ext] ?? 'ti ti-file';
                                                            ?>

                                                            <div class="d-flex align-items-center mb-2">
                                                                <a href="<?php echo e(asset('storage/' . $field['value'])); ?>"
                                                                    target="_blank"
                                                                    class="d-flex align-items-center text-decoration-none">
                                                                    <i class="<?php echo e($iconClass); ?> me-2 fs-4 text-primary"></i>
                                                                    <span
                                                                        class="text-truncate"><?php echo e(basename($field['value'])); ?></span>
                                                                </a>
                                                            </div>
                                                        <?php endif; ?>

                                                        <?php if(isset($field['expiration']) && $field['expiration']): ?>
                                                            <div class="mt-2">
                                                                <small class="text-muted">Expires:</small>
                                                                <p class="mb-0 fw-medium">
                                                                    <?php echo e(\Carbon\Carbon::parse($field['expiration'])->format('Y-m-d')); ?>

                                                                </p>
                                                            </div>
                                                        <?php endif; ?>
                                                    <?php break; ?>

                                                    <?php default: ?>
                                                        <p class="mb-0"><?php echo e($field['value']); ?></p>
                                                <?php endswitch; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info" role="alert">
                                    <?php echo e(__('No additional data found for this customer.')); ?>

                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- سجل الأحداث -->
            <div class="col-lg-4 col-md-12">
                <div class="card info-card">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0 text-dark">
                            <i class="fas fa-history me-2 text-primary"></i>
                            <?php echo e(__('Customs Clearance History')); ?>

                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if($data->history->count()): ?>
                            <ul class="timeline">
                                <?php $__currentLoopData = $data->history->sortByDesc('created_at'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $entry): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="timeline-item">
                                        <span class="timeline-point"></span>
                                        <div class="timeline-event">
                                            <div class="d-flex justify-content-between mb-1">
                                                <strong><?php echo e(ucfirst($entry->action_type)); ?></strong>
                                                <small
                                                    class="text-muted"><?php echo e($entry->created_at->format('Y-m-d H:i')); ?></small>
                                            </div>
                                            <?php if($entry->user_id && $entry->clearance_agent_id): ?>
                                                <p>
                                                    <small class="text-muted"><?php echo e(__('By')); ?>:
                                                        <?php echo e($entry->user?->name); ?></small>
                                                </p>
                                                <p>
                                                    <small class="text-muted"><?php echo e(__('To')); ?>:
                                                        <?php echo e($entry->clearanceAgent->name); ?></small>
                                                </p>
                                            <?php elseif($entry->user_id): ?>
                                                <p>
                                                    <small class="text-muted"><?php echo e(__('By')); ?>:
                                                        <?php echo e($entry->user?->name); ?></small>
                                                </p>
                                            <?php elseif($entry->clearance_agent_id): ?>
                                                <p>
                                                    <small class="text-muted"><?php echo e(__('By')); ?>:
                                                        <?php echo e($entry->clearanceAgent->name); ?></small>
                                                </p>
                                            <?php endif; ?>

                                            <p class="mb-1"><?php echo e($entry->description); ?></p>
                                            <?php if($entry->file_path): ?>
                                                <a href="<?php echo e(asset('storage/' . $entry->file_path)); ?>" target="_blank"
                                                    class="btn btn-sm btn-outline-primary"><?php echo e(__('download file')); ?></a>
                                            <?php endif; ?>


                                        </div>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        <?php else: ?>
                            <p class="text-muted"><?php echo e(__('No History Recorded Yet')); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts/layoutMaster', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\safedestsss\resources\views/admin/customs-clearances/show.blade.php ENDPATH**/ ?>